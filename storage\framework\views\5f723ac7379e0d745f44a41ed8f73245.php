<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['booking', 'height' => '400px', 'showDetails' => true]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['booking', 'height' => '400px', 'showDetails' => true]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="bg-white rounded-xl shadow-sm overflow-hidden">
    <?php if($showDetails): ?>
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Live Tracking</h3>
                    <p class="text-sm text-gray-600"><?php echo e($booking->booking_id); ?></p>
                </div>
                <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php switch($booking->status):
                            case ('confirmed'): ?>
                                bg-blue-100 text-blue-800
                                <?php break; ?>

                            <?php case ('in_progress'): ?>
                                bg-purple-100 text-purple-800
                                <?php break; ?>
                            <?php case ('completed'): ?>
                                bg-green-100 text-green-800
                                <?php break; ?>
                            <?php default: ?>
                                bg-gray-100 text-gray-800
                        <?php endswitch; ?>
                    ">
                        <span class="booking-status-text"><?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?></span>
                    </span>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Map Container -->
    <div class="relative">
        <div id="trackingMap<?php echo e($booking->id); ?>" style="height: <?php echo e($height); ?>; width: 100%;"></div>
        
        <!-- Loading Overlay -->
        <div id="mapLoading<?php echo e($booking->id); ?>" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"></div>
                <p class="text-sm text-gray-600">Loading map...</p>
            </div>
        </div>
        
        <!-- Map Controls -->
        <div class="absolute top-4 right-4 space-y-2">
            <button onclick="refreshTrackingMap<?php echo e($booking->id); ?>()" 
                    class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors">
                <i class="fas fa-sync-alt text-gray-600"></i>
            </button>

            <button onclick="showFullRoute<?php echo e($booking->id); ?>()" 
                    class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors">
                <i class="fas fa-route text-gray-600"></i>
            </button>
        </div>
    </div>
    
    <?php if($showDetails): ?>
        <!-- Tracking Details -->
        <div class="p-6 bg-gray-50">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Route Information -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Route Information</h4>
                    <div class="space-y-2">
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-green-500 rounded-full mt-1 mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Pickup</p>
                                <p class="text-xs text-gray-600"><?php echo e($booking->pickup_address); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($booking->pickup_person_name); ?> - <?php echo e($booking->pickup_person_phone); ?></p>
                            </div>
                        </div>
                        <div class="flex items-center ml-1.5">
                            <div class="w-px h-6 bg-gray-300"></div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-red-500 rounded-full mt-1 mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">Delivery</p>
                                <p class="text-xs text-gray-600"><?php echo e($booking->delivery_address); ?></p>
                                <p class="text-xs text-gray-500"><?php echo e($booking->receiver_name); ?> - <?php echo e($booking->receiver_phone); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Delivery Information -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">Delivery Status</h4>
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-truck text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900"><?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?></p>
                            <p class="text-xs text-gray-600">Booking #<?php echo e($booking->booking_id); ?></p>
                        </div>
                    </div>

                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1"></span>
                                    <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">Created</span>
                                <span class="text-xs text-gray-900"><?php echo e($booking->created_at->diffForHumans()); ?></span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-gray-500">ETA</span>
                                <span class="text-xs text-gray-900" id="eta<?php echo e($booking->id); ?>">Calculating...</span>
                            </div>
                        </div>
                </div>
            </div>
            
            <!-- Progress Timeline -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="font-medium text-gray-900 mb-3">Delivery Progress</h4>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 <?php echo e(in_array($booking->status, ['confirmed', 'delivery_enroute', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300'); ?> rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Confirmed</span>
                    </div>
                    <div class="flex-1 h-px <?php echo e(in_array($booking->status, ['delivery_enroute', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300'); ?>"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 <?php echo e(in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300'); ?> rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Delivery Enroute</span>
                    </div>
                    <div class="flex-1 h-px <?php echo e(in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300'); ?>"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 <?php echo e(in_array($booking->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300'); ?> rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">In Transit</span>
                    </div>
                    <div class="flex-1 h-px <?php echo e($booking->status === 'completed' ? 'bg-green-500' : 'bg-gray-300'); ?>"></div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 <?php echo e($booking->status === 'completed' ? 'bg-green-500' : 'bg-gray-300'); ?> rounded-full"></div>
                        <span class="ml-2 text-xs text-gray-600">Delivered</span>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Initialize tracking map for this booking
function initTrackingMap<?php echo e($booking->id); ?>() {
    const mapElement = document.getElementById('trackingMap<?php echo e($booking->id); ?>');
    const loadingElement = document.getElementById('mapLoading<?php echo e($booking->id); ?>');
    
    if (!mapElement) return;
    
    // Default center between pickup and delivery
    const pickupLat = <?php echo e($booking->pickup_latitude ?? 5.6037); ?>;
    const pickupLng = <?php echo e($booking->pickup_longitude ?? -0.1870); ?>;
    const deliveryLat = <?php echo e($booking->delivery_latitude ?? 5.6037); ?>;
    const deliveryLng = <?php echo e($booking->delivery_longitude ?? -0.1870); ?>;
    
    const centerLat = (pickupLat + deliveryLat) / 2;
    const centerLng = (pickupLng + deliveryLng) / 2;
    
    const map<?php echo e($booking->id); ?> = new google.maps.Map(mapElement, {
        zoom: 13,
        center: { lat: centerLat, lng: centerLng },
        mapTypeId: google.maps.MapTypeId.ROADMAP
    });
    
    // Store map reference globally
    window.trackingMap<?php echo e($booking->id); ?> = map<?php echo e($booking->id); ?>;
    let vehicleMarker<?php echo e($booking->id); ?> = null;
    
    // Add pickup marker
    const pickupMarker = new google.maps.Marker({
        position: { lat: pickupLat, lng: pickupLng },
        map: map<?php echo e($booking->id); ?>,
        title: 'Pickup Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });
    
    // Add delivery marker
    const deliveryMarker = new google.maps.Marker({
        position: { lat: deliveryLat, lng: deliveryLng },
        map: map<?php echo e($booking->id); ?>,
        title: 'Delivery Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                    <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });
    

        
    // Initial route calculation
    calculateAndDisplayRoute<?php echo e($booking->id); ?>(pickupLat, pickupLng, deliveryLat, deliveryLng);

    // Start live tracking
    setInterval(() => refreshTrackingMap<?php echo e($booking->id); ?>(), 15000); // Refresh every 15 seconds
    
    // Info windows
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Pickup Location</h4>
                <p class="text-sm"><?php echo e($booking->pickup_address); ?></p>
                <p class="text-xs text-gray-600"><?php echo e($booking->pickup_person_name); ?></p>
            </div>
        `
    });
    
    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Delivery Location</h4>
                <p class="text-sm"><?php echo e($booking->delivery_address); ?></p>
                <p class="text-xs text-gray-600"><?php echo e($booking->receiver_name); ?></p>
            </div>
        `
    });
    
    pickupMarker.addListener('click', () => {
        pickupInfoWindow.open(map<?php echo e($booking->id); ?>, pickupMarker);
    });
    
    deliveryMarker.addListener('click', () => {
        deliveryInfoWindow.open(map<?php echo e($booking->id); ?>, deliveryMarker);
    });
    
    // Fit map to show all markers
    const bounds = new google.maps.LatLngBounds();
    bounds.extend({ lat: pickupLat, lng: pickupLng });
    bounds.extend({ lat: deliveryLat, lng: deliveryLng });

    map<?php echo e($booking->id); ?>.fitBounds(bounds);
    
    // Hide loading overlay
    loadingElement.style.display = 'none';
}

// Refresh tracking map
function refreshTrackingMap<?php echo e($booking->id); ?>() {
    fetch(`/api/live-map/booking/<?php echo e($booking->id); ?>`)
        .then(response => response.json())
        .then(data => {
            // Update booking status if changed
            const statusElement = document.querySelector('.booking-status-text');
            if (statusElement && data.status) {
                statusElement.textContent = data.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            }

            // Update any other real-time data as needed
            console.log('📍 Tracking data updated for booking:', data.booking_id);
        })
        .catch(error => console.error('Error refreshing tracking data:', error));
}

function calculateAndDisplayRoute<?php echo e($booking->id); ?>(originLat, originLng, destLat, destLng) {
    const directionsService = new google.maps.DirectionsService();
    const directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true,
        polylineOptions: {
            strokeColor: '#F59E0B',
            strokeWeight: 4
        }
    });
    directionsRenderer.setMap(map<?php echo e($booking->id); ?>);

    directionsService.route({
        origin: { lat: originLat, lng: originLng },
        destination: { lat: destLat, lng: destLng },
        travelMode: google.maps.TravelMode.DRIVING
    }, (result, status) => {
        if (status === 'OK') {
            directionsRenderer.setDirections(result);
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const etaElement = document.getElementById('eta<?php echo e($booking->id); ?>');
                if (etaElement) {
                    etaElement.textContent = leg.duration.text;
                }
            }
        }
    });
}



// Show full route
function showFullRoute<?php echo e($booking->id); ?>() {
    if (window.trackingMap<?php echo e($booking->id); ?>) {
        const bounds = new google.maps.LatLngBounds();
        bounds.extend({ lat: <?php echo e($booking->pickup_latitude ?? 5.6037); ?>, lng: <?php echo e($booking->pickup_longitude ?? -0.1870); ?> });
        bounds.extend({ lat: <?php echo e($booking->delivery_latitude ?? 5.6037); ?>, lng: <?php echo e($booking->delivery_longitude ?? -0.1870); ?> });

        window.trackingMap<?php echo e($booking->id); ?>.fitBounds(bounds);
    }
}

// Initialize when Google Maps is loaded
if (typeof google !== 'undefined' && google.maps) {
    initTrackingMap<?php echo e($booking->id); ?>();
} else {
    // Wait for Google Maps to load
    window.addEventListener('load', () => {
        if (typeof google !== 'undefined' && google.maps) {
            initTrackingMap<?php echo e($booking->id); ?>();
        }
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/components/live-tracking-card.blade.php ENDPATH**/ ?>