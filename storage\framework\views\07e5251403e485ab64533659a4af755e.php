<?php $__env->startSection('title', 'Booking Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">

            <div class="text-center mb-8">
                <h1 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                    Booking Details
                </h1>
                <p class="mt-4 text-lg text-gray-600">
                    Booking ID: <span class="font-semibold text-indigo-600"><?php echo e($booking->booking_id); ?></span>
                </p>
            </div>


    <div class="dubble-view" style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; max-width: 95vw;">

        
        <!-- Live Tracking Map -->
            <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
                <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">
                                    <i class="fas fa-map-marker-alt text-orange-600 mr-2"></i>
                                    Live Tracking
                                </h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                                    Real-time location and delivery progress
                                </p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <button onclick="refreshTracking()"
                                        class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                    <i class="fas fa-sync-alt mr-1" id="refresh-icon"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Quick Status Info -->
                    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-3 h-3 rounded-full
                                        <?php switch($booking->status):
                                            case ('confirmed'): ?>
                                                bg-blue-400
                                                <?php break; ?>

                                            <?php case ('in_progress'): ?>
                                                bg-purple-400 animate-pulse
                                                <?php break; ?>
                                            <?php default: ?>
                                                bg-gray-400
                                        <?php endswitch; ?>
                                    "></div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">
                                        <?php switch($booking->status):
                                            case ('confirmed'): ?>
                                                Preparing for Pickup
                                                <?php break; ?>
                                            <?php case ('in_progress'): ?>
                                                Package Being Delivered
                                                <?php break; ?>
                                            <?php default: ?>
                                                <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                        <?php endswitch; ?>
                                    </p>
                                </div>
                            </div>

                            <?php if($booking->estimated_duration_minutes): ?>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-clock text-gray-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            ~<?php echo e($booking->estimated_duration_minutes); ?> minutes
                                        </p>
                                        <p class="text-xs text-gray-500">Estimated delivery time</p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($booking->distance_km): ?>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-route text-gray-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php echo e(number_format($booking->distance_km, 1)); ?> km
                                        </p>
                                        <p class="text-xs text-gray-500">Total distance</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="p-0">
                        <?php if (isset($component)) { $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.live-tracking-card','data' => ['booking' => $booking,'height' => '500px','showDetails' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('live-tracking-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['booking' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($booking),'height' => '500px','showDetails' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $attributes = $__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__attributesOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9)): ?>
<?php $component = $__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9; ?>
<?php unset($__componentOriginalbd8dc289ba51619dbf13cb5d6fad94e9); ?>
<?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

        <div class="min-w-full mx-auto">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        Trip Information
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        Current status and details of your booking.
                    </p>
                </div>
                <div class="border-t border-gray-200">
                    <dl>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php switch($booking->status):
                                        case ('pending'): ?>
                                            bg-yellow-100 text-yellow-800
                                            <?php break; ?>
                                        <?php case ('confirmed'): ?>
                                            bg-blue-100 text-blue-800
                                            <?php break; ?>
                                        <?php case ('in_progress'): ?>
                                            bg-purple-100 text-purple-800
                                            <?php break; ?>
                                        <?php case ('completed'): ?>
                                            bg-green-100 text-green-800
                                            <?php break; ?>
                                        <?php case ('cancelled'): ?>
                                            bg-red-100 text-red-800
                                            <?php break; ?>
                                        <?php default: ?>
                                            bg-gray-100 text-gray-800
                                    <?php endswitch; ?>
                                ">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $booking->status))); ?>

                                </span>
                            </dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Pickup Location</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?php echo e($booking->pickup_address); ?></dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Destination</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?php echo e($booking->delivery_address); ?></dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Scheduled Time</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <?php echo e($booking->scheduled_pickup_time ? $booking->scheduled_pickup_time->format('M d, Y \a\t g:i A') : 'ASAP'); ?>

                            </dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?php echo e(ucfirst($booking->package_type)); ?></dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Total Cost</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <span class="text-lg font-semibold text-green-600">₱<?php echo e(number_format($booking->final_cost ?? $booking->estimated_cost, 2)); ?></span>
                            </dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <?php echo e($booking->created_at->format('M d, Y \a\t g:i A')); ?>

                            </dd>
                        </div>
                        <?php if($booking->delivered_at): ?>
                            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                <dt class="text-sm font-medium text-gray-500">Delivered</dt>
                                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                    <?php echo e($booking->delivered_at->format('M d, Y \a\t g:i A')); ?>

                                </dd>
                            </div>
                        <?php endif; ?>
                    </dl>
                </div>
            </div>

            

            <!-- Status Timeline -->
            <?php if($booking->status !== 'pending'): ?>
                <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Status Timeline
                        </h3>
                    </div>
                    <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                        <div class="flow-root">
                            <ul class="-mb-8">
                                <li>
                                    <div class="relative pb-8">
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                <div>
                                                    <p class="text-sm text-gray-500">Booking created</p>
                                                </div>
                                                <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                    <?php echo e($booking->created_at->format('M d, g:i A')); ?>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                
                                <?php if(in_array($booking->status, ['confirmed', 'in_progress', 'completed'])): ?>
                                    <li>
                                        <div class="relative pb-8">
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                        <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                        </svg>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-500">Booking confirmed</p>
                                                    </div>
                                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                        <?php echo e($booking->updated_at->format('M d, g:i A')); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                <?php endif; ?>



                                <?php if($booking->status === 'completed'): ?>
                                    <li>
                                        <div class="relative">
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                        <svg class="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                                        </svg>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                                    <div>
                                                        <p class="text-sm text-gray-500">Trip completed</p>
                                                    </div>
                                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                        <?php echo e($booking->delivered_at ? $booking->delivered_at->format('M d, g:i A') : $booking->updated_at->format('M d, g:i A')); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Actions -->
            <div class="mt-8 text-center">
                <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
                    <a href="<?php echo e(route('tracking')); ?>" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                        </svg>
                        Track Another Booking
                    </a>
                    
                    <a href="<?php echo e(route('home')); ?>" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                        </svg>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>

        
    </div>

</div>

<script>
// Auto-refresh for active bookings
<?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
    let autoRefreshInterval;
    let refreshCount = 0;
    const maxRefreshes = 120; // Stop auto-refresh after 2 hours (120 * 60 seconds)

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            refreshCount++;
            if (refreshCount >= maxRefreshes) {
                stopAutoRefresh();
                return;
            }

            // Refresh the live tracking map if it exists
            if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
                refreshTrackingMap<?php echo e($booking->id); ?>();
            }

            // Optionally refresh the entire page every 10 minutes for status updates
            if (refreshCount % 10 === 0) {
                checkForStatusUpdate();
            }
        }, 60000); // Refresh every 60 seconds
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    function checkForStatusUpdate() {
        // Check if booking status has changed
        fetch(`/api/booking/<?php echo e($booking->id); ?>/status`)
            .then(response => response.json())
            .then(data => {
                if (data.status !== '<?php echo e($booking->status); ?>') {
                    // Status changed, reload the page
                    window.location.reload();
                }
            })
            .catch(error => {
                console.log('Status check failed:', error);
            });
    }

    // Start auto-refresh when page loads
    document.addEventListener('DOMContentLoaded', function() {
        startAutoRefresh();
    });

    // Stop auto-refresh when page is hidden/user switches tabs
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // Stop auto-refresh when user leaves the page
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });
<?php endif; ?>

function refreshTracking() {
    const refreshIcon = document.getElementById('refresh-icon');

    // Add spinning animation
    refreshIcon.classList.add('fa-spin');

    // Refresh the live tracking map
    if (typeof refreshTrackingMap<?php echo e($booking->id); ?> === 'function') {
        refreshTrackingMap<?php echo e($booking->id); ?>();
    }

    // Check for status updates
    <?php if(in_array($booking->status, ['confirmed', 'in_progress'])): ?>
        checkForStatusUpdate();
    <?php endif; ?>

    // Remove spinning animation after 2 seconds
    setTimeout(() => {
        refreshIcon.classList.remove('fa-spin');
    }, 2000);
}

// Show last updated time
function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();

    // Create or update last refresh indicator
    let refreshIndicator = document.getElementById('last-refresh');
    if (!refreshIndicator) {
        refreshIndicator = document.createElement('div');
        refreshIndicator.id = 'last-refresh';
        refreshIndicator.className = 'text-xs text-gray-500 mt-2';

        const trackingCard = document.querySelector('.live-tracking-card');
        if (trackingCard) {
            trackingCard.appendChild(refreshIndicator);
        }
    }

    refreshIndicator.textContent = `Last updated: ${timeString}`;
}

// Update last refresh time on page load
document.addEventListener('DOMContentLoaded', function() {
    updateLastRefreshTime();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/tracking-result.blade.php ENDPATH**/ ?>