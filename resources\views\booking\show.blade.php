@extends('layouts.app')

@section('title', 'Booking Details - ' . $booking->booking_id)

@section('content')
<div class="min-h-screen bg-gray-50">
    
    <!-- Header -->
    <div class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Booking Details</h1>
                    <p class="text-gray-600 mt-1">{{ $booking->booking_id }}</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-4">
                    <a href="{{ route('booking.history') }}" 
                       class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold">
                        <i class="fas fa-arrow-left mr-2"></i>Back to History
                    </a>
                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                        <a href="{{ route('tracking') }}?booking_id={{ $booking->booking_id }}" 
                           class="brand-orange text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors font-semibold">
                            <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 py-8">
        <div class="max-w-6xl mx-auto">
            
            <!-- Status Banner -->
            <div class="mb-8 p-6 rounded-xl
                @switch($booking->status)
                    @case('pending')
                        bg-yellow-50 border border-yellow-200
                        @break
                    @case('confirmed')
                        bg-blue-50 border border-blue-200
                        @break
                    @case('in_progress')
                        bg-purple-50 border border-purple-200
                        @break
                    @case('completed')
                        bg-green-50 border border-green-200
                        @break
                    @case('cancelled')
                        bg-red-50 border border-red-200
                        @break
                    @default
                        bg-gray-50 border border-gray-200
                @endswitch
            ">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full
                            @switch($booking->status)
                                @case('pending')
                                    bg-yellow-100
                                    @break
                                @case('confirmed')
                                    bg-blue-100
                                    @break
                                @case('in_progress')
                                    bg-purple-100
                                    @break
                                @case('completed')
                                    bg-green-100
                                    @break
                                @case('cancelled')
                                    bg-red-100
                                    @break
                                @default
                                    bg-gray-100
                            @endswitch
                        ">
                            <i class="fas 
                                @switch($booking->status)
                                    @case('pending')
                                        fa-clock text-yellow-600
                                        @break
                                    @case('confirmed')
                                        fa-check text-blue-600
                                        @break
                                    @case('in_progress')
                                        fa-truck text-purple-600
                                        @break
                                    @case('completed')
                                        fa-check-circle text-green-600
                                        @break
                                    @case('cancelled')
                                        fa-times-circle text-red-600
                                        @break
                                    @default
                                        fa-box text-gray-600
                                @endswitch
                                text-xl
                            "></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-bold
                                @switch($booking->status)
                                    @case('pending')
                                        text-yellow-900
                                        @break
                                    @case('confirmed')
                                        text-blue-900
                                        @break
                                    @case('in_progress')
                                        text-purple-900
                                        @break
                                    @case('completed')
                                        text-green-900
                                        @break
                                    @case('cancelled')
                                        text-red-900
                                        @break
                                    @default
                                        text-gray-900
                                @endswitch
                            ">
                                {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                            </h3>
                            <p class="text-sm
                                @switch($booking->status)
                                    @case('pending')
                                        text-yellow-700
                                        @break
                                    @case('confirmed')
                                        text-blue-700
                                        @break
                                    @case('in_progress')
                                        text-purple-700
                                        @break
                                    @case('completed')
                                        text-green-700
                                        @break
                                    @case('cancelled')
                                        text-red-700
                                        @break
                                    @default
                                        text-gray-700
                                @endswitch
                            ">
                                @switch($booking->status)
                                    @case('pending')
                                        Your booking is waiting for confirmation
                                        @break
                                    @case('confirmed')
                                        Your booking has been confirmed and is being processed
                                        @break
                                    @case('in_progress')
                                        Your package is on the way
                                        @break
                                    @case('completed')
                                        Your package has been delivered successfully
                                        @break
                                    @case('cancelled')
                                        This booking has been cancelled
                                        @break
                                    @default
                                        Booking status: {{ $booking->status }}
                                @endswitch
                            </p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600">Created</p>
                        <p class="font-medium text-gray-900">{{ $booking->created_at->format('M d, Y') }}</p>
                        <p class="text-xs text-gray-500">{{ $booking->created_at->format('h:i A') }}</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    
                    <!-- Distance & Cost Preview -->
                    <div id="route-preview" class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="bg-blue-100 rounded-full p-3">
                                    <i class="fas fa-route text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-600">Estimated Distance</p>
                                    <p class="text-lg font-semibold text-gray-900" id="estimated-distance">{{ $booking->distance ?? '-- km' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-gray-600">Estimated Duration</p>
                                <p class="text-lg font-semibold text-gray-900" id="estimated-duration">{{ $booking->duration ?? '-- mins' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Map Preview -->
                    <div id="map-preview" class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-semibold text-gray-700">
                                <i class="fas fa-map text-orange-500 mr-2"></i>Route Preview
                            </h4>
                            <button type="button" id="toggle-map" class="text-xs text-orange-600 hover:text-orange-700 font-medium">
                                <i class="fas fa-expand-arrows-alt mr-1"></i>Expand
                            </button>
                        </div>
                        <div id="booking-map" style="height: 200px; width: 100%;" class="rounded-lg border border-gray-200"></div>
                    </div>

                    <!-- Live Tracking Map -->
                    @if(in_array($booking->status, ['confirmed', 'in_progress']))
                        <x-live-tracking-card :booking="$booking" height="500px" />
                    @endif
                    
                    <!-- Route Information -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Route Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-6">
                                <!-- Pickup -->
                                <div class="flex items-start">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mt-1 mr-4"></div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Pickup Location</h4>
                                        <p class="text-gray-600 mt-1">{{ $booking->pickup_address }}</p>
                                        <div class="mt-2 text-sm text-gray-500">
                                            <p><strong>Contact:</strong> {{ $booking->pickup_person_name }}</p>
                                            <p><strong>Phone:</strong> {{ $booking->pickup_person_phone }}</p>
                                        </div>
                                        @if($booking->actual_pickup_time)
                                            <p class="text-xs text-green-600 mt-2">
                                                <i class="fas fa-check mr-1"></i>
                                                Picked up at {{ $booking->actual_pickup_time->format('M d, Y h:i A') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                                
                                <!-- Route Line -->
                                <div class="flex items-center ml-2">
                                    <div class="w-px h-8 bg-gray-300"></div>
                                </div>
                                
                                <!-- Delivery -->
                                <div class="flex items-start">
                                    <div class="w-4 h-4 bg-red-500 rounded-full mt-1 mr-4"></div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">Delivery Location</h4>
                                        <p class="text-gray-600 mt-1">{{ $booking->delivery_address }}</p>
                                        <div class="mt-2 text-sm text-gray-500">
                                            <p><strong>Receiver:</strong> {{ $booking->receiver_name }}</p>
                                            <p><strong>Phone:</strong> {{ $booking->receiver_phone }}</p>
                                        </div>
                                        @if($booking->delivered_at)
                                            <p class="text-xs text-green-600 mt-2">
                                                <i class="fas fa-check mr-1"></i>
                                                Delivered at {{ $booking->delivered_at->format('M d, Y h:i A') }}
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Package Information -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Package Information</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Package Type</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($booking->package_type) }}</dd>
                                </div>
                                @if($booking->package_weight)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Weight</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_weight }} kg</dd>
                                    </div>
                                @endif
                                @if($booking->package_description)
                                    <div class="md:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->package_description }}</dd>
                                    </div>
                                @endif
                                @if($booking->special_instructions)
                                    <div class="md:col-span-2">
                                        <dt class="text-sm font-medium text-gray-500">Special Instructions</dt>
                                        <dd class="mt-1 text-sm text-gray-900">{{ $booking->special_instructions }}</dd>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="space-y-6">
                    
                    <!-- Booking Summary -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Booking Summary</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Booking ID</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->booking_id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Distance</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->distance_km ?? 'N/A' }} km</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Estimated Duration</span>
                                <span class="text-sm font-medium text-gray-900">{{ $booking->estimated_duration_minutes ?? 'N/A' }} min</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Method</span>
                                <span class="text-sm font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $booking->payment_method)) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Payment Status</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    @switch($booking->payment_status)
                                        @case('paid')
                                            bg-green-100 text-green-800
                                            @break
                                        @case('pending')
                                            bg-yellow-100 text-yellow-800
                                            @break
                                        @case('failed')
                                            bg-red-100 text-red-800
                                            @break
                                        @default
                                            bg-gray-100 text-gray-800
                                    @endswitch
                                ">
                                    {{ ucfirst($booking->payment_status) }}
                                </span>
                            </div>
                            <hr>
                            <div class="flex justify-between items-center">
                                <span class="text-base font-medium text-gray-900">Total Cost</span>
                                <span class="text-lg font-bold text-green-600">CF$ {{ number_format($booking->final_cost ?? $booking->estimated_cost, 2) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Status -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-bold text-gray-900">Delivery Status</h3>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-truck text-blue-600 text-lg"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="font-medium text-gray-900">{{ ucfirst(str_replace('_', ' ', $booking->status)) }}</h4>
                                    <p class="text-sm text-gray-600">Booking #{{ $booking->booking_id }}</p>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Status</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <span class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1"></span>
                                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Created</span>
                                    <span class="text-sm text-gray-900">{{ $booking->created_at->diffForHumans() }}</span>
                                </div>
                                @if($booking->estimated_duration_minutes)
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">Estimated Duration</span>
                                        <span class="text-sm text-gray-900">{{ $booking->estimated_duration_minutes }} minutes</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="p-6">
                            <div class="space-y-3">
                                @if(in_array($booking->status, ['confirmed', 'in_progress']))
                                    <a href="{{ route('tracking') }}?booking_id={{ $booking->booking_id }}" 
                                       class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white brand-orange hover:bg-orange-600">
                                        <i class="fas fa-map-marker-alt mr-2"></i>Live Tracking
                                    </a>
                                @endif
                                
                                @if($booking->status === 'completed' && !$booking->review)
                                    <button onclick="openReviewModal()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-star mr-2"></i>Leave Review
                                    </button>
                                @endif
                                
                                @if($booking->status === 'pending')
                                    <button onclick="cancelBooking()" 
                                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50">
                                        <i class="fas fa-times mr-2"></i>Cancel Booking
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Google Maps API -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=places&callback=initGoogleMaps"></script>

<script>
// Map variables
let map;
let directionsService;
let directionsRenderer;
let pickupMarker;
let deliveryMarker;

// Initialize Google Maps when API is loaded
function initGoogleMaps() {
    console.log('Google Maps API loaded');
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeBookingDetailsMap);
    } else {
        initializeBookingDetailsMap();
    }
}

function initializeBookingDetailsMap() {
    if (typeof google === 'undefined' || !google.maps) {
        console.warn('Google Maps API not loaded yet');
        return;
    }

    // Initialize map
    const mapElement = document.getElementById('booking-map');
    if (mapElement) {
        map = new google.maps.Map(mapElement, {
            zoom: 12,
            center: { lat: 5.6037, lng: -0.1870 }, // Accra, Ghana
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ]
        });

        directionsService = new google.maps.DirectionsService();
        directionsRenderer = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            polylineOptions: {
                strokeColor: '#F59E0B',
                strokeWeight: 4
            }
        });
        directionsRenderer.setMap(map);

        // Load booking route
        updateMapAndRoute();
    }

    // Map toggle functionality
    document.getElementById('toggle-map').addEventListener('click', function() {
        const mapContainer = document.getElementById('booking-map');
        const button = this;

        if (mapContainer.style.height === '200px') {
            mapContainer.style.height = '400px';
            button.innerHTML = '<i class="fas fa-compress-arrows-alt mr-1"></i>Collapse';
        } else {
            mapContainer.style.height = '200px';
            button.innerHTML = '<i class="fas fa-expand-arrows-alt mr-1"></i>Expand';
        }

        // Trigger map resize
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                if (pickupMarker && deliveryMarker) {
                    const bounds = new google.maps.LatLngBounds();
                    bounds.extend(pickupMarker.getPosition());
                    bounds.extend(deliveryMarker.getPosition());
                    map.fitBounds(bounds);
                }
            }
        }, 300);
    });
}

function updateMapAndRoute() {
    const pickupLat = {{ $booking->pickup_latitude ?? 5.6037 }};
    const pickupLng = {{ $booking->pickup_longitude ?? -0.1870 }};
    const deliveryLat = {{ $booking->delivery_latitude ?? 5.6037 }};
    const deliveryLng = {{ $booking->delivery_longitude ?? -0.1870 }};

    if (!map || isNaN(pickupLat) || isNaN(pickupLng) || isNaN(deliveryLat) || isNaN(deliveryLng)) {
        return;
    }

    // Clear existing markers
    if (pickupMarker) pickupMarker.setMap(null);
    if (deliveryMarker) deliveryMarker.setMap(null);

    // Add pickup marker
    pickupMarker = new google.maps.Marker({
        position: { lat: pickupLat, lng: pickupLng },
        map: map,
        title: 'Pickup Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#10B981" stroke="white" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add delivery marker
    deliveryMarker = new google.maps.Marker({
        position: { lat: deliveryLat, lng: deliveryLng },
        map: map,
        title: 'Delivery Location',
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#EF4444" stroke="white" stroke-width="2"/>
                    <path d="M12 8v4l3 3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(32, 32)
        }
    });

    // Add info windows
    const pickupInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Pickup Location</h4>
                <p class="text-sm">{{ $booking->pickup_address }}</p>
                <p class="text-xs text-gray-600">{{ $booking->sender_name }}</p>
            </div>
        `
    });

    const deliveryInfoWindow = new google.maps.InfoWindow({
        content: `
            <div class="p-2">
                <h4 class="font-bold">Delivery Location</h4>
                <p class="text-sm">{{ $booking->delivery_address }}</p>
                <p class="text-xs text-gray-600">{{ $booking->receiver_name }}</p>
            </div>
        `
    });

    pickupMarker.addListener('click', () => {
        pickupInfoWindow.open(map, pickupMarker);
    });

    deliveryMarker.addListener('click', () => {
        deliveryInfoWindow.open(map, deliveryMarker);
    });

    // Calculate and display route
    directionsService.route({
        origin: { lat: pickupLat, lng: pickupLng },
        destination: { lat: deliveryLat, lng: deliveryLng },
        travelMode: google.maps.TravelMode.DRIVING
    }, function(result, status) {
        if (status === 'OK') {
            directionsRenderer.setDirections(result);

            // Update distance and duration if not already set
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const distanceElement = document.getElementById('estimated-distance');
                const durationElement = document.getElementById('estimated-duration');

                if (distanceElement.textContent === '-- km') {
                    distanceElement.textContent = leg.distance.text;
                }
                if (durationElement.textContent === '-- mins') {
                    durationElement.textContent = leg.duration.text;
                }
            }

            // Fit map to show route
            const bounds = new google.maps.LatLngBounds();
            bounds.extend({ lat: pickupLat, lng: pickupLng });
            bounds.extend({ lat: deliveryLat, lng: deliveryLng });
            map.fitBounds(bounds);
        }
    });
}

// Auto-refresh for active bookings
@if(in_array($booking->status, ['confirmed', 'in_progress']))
    setInterval(() => {
        // Refresh tracking component if it exists
        if (typeof refreshTrackingMap{{ $booking->id }} === 'function') {
            refreshTrackingMap{{ $booking->id }}();
        }
    }, 30000); // Refresh every 30 seconds
@endif

function openReviewModal() {
    // Placeholder for review modal
    alert('Review functionality will be implemented soon!');
}

function cancelBooking() {
    if (confirm('Are you sure you want to cancel this booking?')) {
        // Implement cancellation logic
        alert('Cancellation functionality will be implemented soon!');
    }
}
</script>
@endsection
